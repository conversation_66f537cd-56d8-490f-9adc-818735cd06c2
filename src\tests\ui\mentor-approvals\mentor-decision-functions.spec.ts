import { test } from '@playwright/test';
import { LoginPage } from '../../../pages/login/login-page';
import { MentorApprovalsPage } from '../../../pages/mentor-approvals/mentor-approvals-page';
import { MentorDetailPage } from '../../../pages/mentor-approvals/mentor-detail-page';
import testData from '../../../tests-data/mentor-approvals-data.json';
import { MentorApprovalsTestData, MentorDecisionTestData } from '../../../data-type/mentor-approvals.type';

test.describe('Mentor Decision Making Functions Tests', () => {
    let loginPage: LoginPage;
    let mentorApprovalsPage: MentorApprovalsPage;
    let mentorDetailPage: MentorDetailPage;
    const decisionData: MentorApprovalsTestData = testData as MentorApprovalsTestData;

    test.beforeEach(async ({ page }) => {
        loginPage = new LoginPage(page);
        mentorApprovalsPage = new MentorApprovalsPage(page);
        mentorDetailPage = new MentorDetailPage(page);
    });

    test('@MentorDecision Decision Making Functions test - Request Update', async () => {
        // Use MENTORPRO scenario for rejection and request update
        const scenario: MentorDecisionTestData = decisionData.decisionScenarios!.find(s => s.searchTerm === "MENTORPRO")!;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Login with admin credentials (user must be admin)', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                decisionData.adminCredentials.email,
                decisionData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyAdminLoginSuccessfully();
        });

        await test.step('Step 4: Click Mentor Approvals link', async () => {
            await mentorApprovalsPage.clickMentorApprovalsLink();
        });

        await test.step('Make sure it\'s on Mentor Approvals Management page', async () => {
            await mentorApprovalsPage.verifyOnMentorApprovalsPage();
            await mentorApprovalsPage.clickMentorApprovalsHeading();
        });

        await test.step(`Step 5: Search for "${scenario.searchTerm}" from test-data`, async () => {
            // Click search box
            await mentorApprovalsPage.clickSearchBox();

            // Fill search term from test-data
            await mentorApprovalsPage.searchForMentor(scenario.searchTerm);

            // Check search results
            await mentorApprovalsPage.clickFullNameColumnHeader();
            await mentorApprovalsPage.verifyMentorInResults(scenario.mentorDetails.fullName);
            await mentorApprovalsPage.clickOnMentorName(scenario.mentorDetails.fullName);
        });

        await test.step('Step 6: Click view details application button', async () => {
            await mentorApprovalsPage.clickViewDetailsButton(scenario.mentorDetails.fullName);
        });

        if (scenario.decisionActions.shouldTestRequestUpdate) {
            await test.step('Test Request Update function', async () => {
                await mentorDetailPage.performRequestUpdate(scenario.decisionActions.requestUpdateReason);
            });
        }
    });

    test('@MentorDecision Decision Making Functions test - Reject Application', async () => {
        // Use MENTORPRO scenario for rejection and request update
        const scenario: MentorDecisionTestData = decisionData.decisionScenarios!.find(s => s.searchTerm === "MENTORPRO")!;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Login with admin credentials (user must be admin)', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                decisionData.adminCredentials.email,
                decisionData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyAdminLoginSuccessfully();
        });

        await test.step('Step 4: Click Mentor Approvals link', async () => {
            await mentorApprovalsPage.clickMentorApprovalsLink();
        });

        await test.step('Make sure it\'s on Mentor Approvals Management page', async () => {
            await mentorApprovalsPage.verifyOnMentorApprovalsPage();
            await mentorApprovalsPage.clickMentorApprovalsHeading();
        });

        await test.step(`Step 5: Search for "${scenario.searchTerm}" from test-data`, async () => {
            // Click search box
            await mentorApprovalsPage.clickSearchBox();

            // Fill search term from test-data
            await mentorApprovalsPage.searchForMentor(scenario.searchTerm);

            // Check search results
            await mentorApprovalsPage.clickFullNameColumnHeader();
            await mentorApprovalsPage.verifyMentorInResults(scenario.mentorDetails.fullName);
            await mentorApprovalsPage.clickOnMentorName(scenario.mentorDetails.fullName);
        });

        await test.step('Step 6: Click view details application button', async () => {
            await mentorApprovalsPage.clickViewDetailsButton(scenario.mentorDetails.fullName);
        });

        if (scenario.decisionActions.shouldTestRejection) {
            await test.step('Test Reject function', async () => {
                await mentorDetailPage.performRejection(scenario.decisionActions.rejectionReason);
            });
        }
    });

    test('@MentorDecision Decision Making Functions test - Approve Application', async () => {
        // Use MENTORAPPROVE scenario for approval
        const scenario: MentorDecisionTestData = decisionData.decisionScenarios!.find(s => s.searchTerm === "MENTORAPPROVE")!;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Login with admin credentials (user must be admin)', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                decisionData.adminCredentials.email,
                decisionData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyAdminLoginSuccessfully();
        });

        await test.step('Step 4: Click Mentor Approvals link', async () => {
            await mentorApprovalsPage.clickMentorApprovalsLink();
        });

        await test.step('Make sure it\'s on Mentor Approvals Management page', async () => {
            await mentorApprovalsPage.verifyOnMentorApprovalsPage();
            await mentorApprovalsPage.clickMentorApprovalsHeading();
        });

        await test.step(`Step 5: Search for "${scenario.searchTerm}" from test-data`, async () => {
            // Click search box
            await mentorApprovalsPage.clickSearchBox();

            // Fill search term from test-data
            await mentorApprovalsPage.searchForMentor(scenario.searchTerm);

            // Check search results
            await mentorApprovalsPage.clickFullNameColumnHeader();
            await mentorApprovalsPage.verifyMentorInResults(scenario.mentorDetails.fullName);
            await mentorApprovalsPage.clickOnMentorName(scenario.mentorDetails.fullName);
        });

        await test.step('Step 6: Click view details application button', async () => {
            await mentorApprovalsPage.clickViewDetailsButton(scenario.mentorDetails.fullName);
        });

        if (scenario.decisionActions.shouldTestApproval) {
            await test.step('Test Approve function', async () => {
                await mentorDetailPage.performApproval();
            });
        }
    });

});

{"adminCredentials": {"email": "<EMAIL>", "password": "admin123A@"}, "searchScenarios": [{"searchTerm": "<PERSON><PERSON><PERSON>", "expectedResults": [{"fullName": "<PERSON><PERSON><PERSON>", "shouldBeVisible": true}]}, {"searchTerm": "ùng", "expectedResults": [{"fullName": "<PERSON><PERSON> Quang Tùng", "shouldBeVisible": true}]}, {"searchTerm": "NonExistentMentor", "expectedResults": []}], "reviewScenarios": [{"searchTerm": "MENTORVIP", "mentorDetails": {"fullName": "MENTORVIP", "hasApplicantInformation": true, "hasApplicationDecision": true, "hasEducation": true, "hasWorkExperience": true, "hasCertifications": true, "hasMotivation": true, "hasSupportingDocuments": true}}], "decisionScenarios": [{"searchTerm": "MENTORPRO", "mentorDetails": {"fullName": "MENTORPRO", "hasApplicantInformation": true, "hasApplicationDecision": true, "hasEducation": true, "hasWorkExperience": true, "hasCertifications": true, "hasMotivation": true, "hasSupportingDocuments": true}, "decisionActions": {"requestUpdateReason": "You need to update bio", "rejectionReason": "I dont like you", "shouldTestApproval": false, "shouldTestRejection": true, "shouldTestRequestUpdate": true}}, {"searchTerm": "MENTORAPPROVE", "mentorDetails": {"fullName": "MENTORAPPROVE", "hasApplicantInformation": true, "hasApplicationDecision": true, "hasEducation": true, "hasWorkExperience": true, "hasCertifications": true, "hasMotivation": true, "hasSupportingDocuments": true}, "decisionActions": {"requestUpdateReason": "Please provide more details", "rejectionReason": "Application incomplete", "shouldTestApproval": true, "shouldTestRejection": false, "shouldTestRequestUpdate": false}}]}
import { test, expect } from '@playwright/test';
import { HomePage } from '../../../pages/register/home-page';
import { LoginPage } from '../../../pages/login/login-page';
import { MentorApprovalsPage } from '../../../pages/mentor-approvals/mentor-approvals-page';
import testData from '../../../tests-data/mentor-approvals-data.json';
import { MentorApprovalsTestData } from '../../../data-type/mentor-approvals.type';

test.describe('Mentor Review Search Tests', () => {
    let homePage: HomePage;
    let loginPage: LoginPage;
    let mentorApprovalsPage: MentorApprovalsPage;
    const searchData: MentorApprovalsTestData = testData as MentorApprovalsTestData;

    test.beforeEach(async ({ page }) => {
        homePage = new HomePage(page);
        loginPage = new LoginPage(page);
        mentorApprovalsPage = new MentorApprovalsPage(page);
    });

    test('@MentorReview Comprehensive mentor search test using test-data', async ({ page }) => {
        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Login with admin credentials (user must be admin)', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                searchData.adminCredentials.email,
                searchData.adminCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyAdminLoginSuccessfully();
        });

        await test.step('Step 4: Click Mentor Approvals link', async () => {
            await mentorApprovalsPage.clickMentorApprovalsLink();
        });

        await test.step('Make sure it\'s on Mentor Approvals Management page', async () => {
            await mentorApprovalsPage.verifyOnMentorApprovalsPage();
            await mentorApprovalsPage.clickMentorApprovalsHeading();
        });

        // Loop through all search scenarios from test-data
        for (const scenario of searchData.searchScenarios) {
            await test.step(`Step 5: Search for "${scenario.searchTerm}" from test-data`, async () => {
                // Click search box
                await mentorApprovalsPage.clickSearchBox();

                // Fill search term from test-data
                await mentorApprovalsPage.searchForMentor(scenario.searchTerm);
            });

            await test.step(`Check search results for "${scenario.searchTerm}"`, async () => {
                if (scenario.expectedResults.length > 0) {
                    // Click Full Name column header
                    await mentorApprovalsPage.clickFullNameColumnHeader();

                    // Verify each expected result
                    for (const expectedResult of scenario.expectedResults) {
                        if (expectedResult.shouldBeVisible) {
                            await mentorApprovalsPage.verifyMentorInResults(expectedResult.fullName);
                            await mentorApprovalsPage.clickOnMentorName(expectedResult.fullName);
                        }
                    }
                } else {
                    await mentorApprovalsPage.verifyNoResults();
                }
            });

            // Clear search for next iteration (if not the last scenario)
            if (scenario !== searchData.searchScenarios[searchData.searchScenarios.length - 1]) {
                await test.step('Clear search for next test', async () => {
                    await mentorApprovalsPage.clickSearchBox();
                    await mentorApprovalsPage.searchForMentor('');
                });
            }
        }
    });
});

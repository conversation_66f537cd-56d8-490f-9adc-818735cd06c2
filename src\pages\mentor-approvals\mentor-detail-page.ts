import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class MentorDetailPage extends BasePage {
    // Main heading locator
    private readonly mentorNameHeading: Locator;
    
    // Section locators
    private readonly applicantInformationSection: Locator;
    private readonly applicationDecisionSection: Locator;
    private readonly educationSection: Locator;
    private readonly workExperienceSection: Locator;
    private readonly certificationsSection: Locator;
    private readonly motivationSection: Locator;
    private readonly supportingDocumentsSection: Locator;

    constructor(page: Page) {
        super(page);
        this.mentorNameHeading = this.page.getByRole('heading', { name: /^[A-Za-z\s]+$/ });
        this.applicantInformationSection = this.page.getByText('Applicant Information');
        this.applicationDecisionSection = this.page.getByText('Application Decision');
        this.educationSection = this.page.getByText('Education');
        this.workExperienceSection = this.page.getByText('Work Experience');
        this.certificationsSection = this.page.getByText('Certifications');
        this.motivationSection = this.page.getByText('Motivation');
        this.supportingDocumentsSection = this.page.getByText('Supporting Documents');
    }

    async verifyMentorNameHeading(expectedName: string): Promise<void> {
        const nameHeading = this.page.getByRole('heading', { name: expectedName });
        await expect(nameHeading).toBeVisible();
        await nameHeading.click();
    }

    async verifyApplicantInformationSection(): Promise<void> {
        await expect(this.applicantInformationSection).toBeVisible();
        await this.applicantInformationSection.click();
    }

    async verifyApplicationDecisionSection(): Promise<void> {
        await expect(this.applicationDecisionSection).toBeVisible();
        await this.applicationDecisionSection.click();
    }

    async verifyEducationSection(): Promise<void> {
        await expect(this.educationSection).toBeVisible();
        await this.educationSection.click();
    }

    async verifyWorkExperienceSection(): Promise<void> {
        await expect(this.workExperienceSection).toBeVisible();
        await this.workExperienceSection.click();
    }

    async verifyCertificationsSection(): Promise<void> {
        await expect(this.certificationsSection).toBeVisible();
        await this.certificationsSection.click();
    }

    async verifyMotivationSection(): Promise<void> {
        await expect(this.motivationSection).toBeVisible();
        await this.motivationSection.click();
    }

    async verifySupportingDocumentsSection(): Promise<void> {
        await expect(this.supportingDocumentsSection).toBeVisible();
        await this.supportingDocumentsSection.click();
    }

    async verifyAllApplicationSections(): Promise<void> {
        await this.verifyApplicantInformationSection();
        await this.verifyApplicationDecisionSection();
        await this.verifyEducationSection();
        await this.verifyWorkExperienceSection();
        await this.verifyCertificationsSection();
        await this.verifyMotivationSection();
        await this.verifySupportingDocumentsSection();
    }

    async verifyDetailedApplicationView(mentorName: string): Promise<void> {
        // First verify the mentor name heading
        await this.verifyMentorNameHeading(mentorName);
        
        // Then verify all required sections are present
        await this.verifyAllApplicationSections();
    }
}

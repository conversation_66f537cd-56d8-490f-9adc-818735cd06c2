import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class MentorDetailPage extends BasePage {
    // Main heading locator
    private readonly mentorNameHeading: Locator;

    // Section locators
    private readonly applicantInformationSection: Locator;
    private readonly applicationDecisionSection: Locator;
    private readonly educationSection: Locator;
    private readonly workExperienceSection: Locator;
    private readonly certificationsSection: Locator;
    private readonly motivationSection: Locator;
    private readonly supportingDocumentsSection: Locator;

    // Close button locator
    private readonly closeButton: Locator;

    // Decision making buttons
    private readonly approveButton: Locator;
    private readonly rejectButton: Locator;
    private readonly requestUpdateButton: Locator;

    // Request update form elements
    private readonly requestUpdateTextbox: Locator;
    private readonly requestUpdateSubmitButton: Locator;

    // Reject form elements
    private readonly rejectionReasonTextbox: Locator;
    private readonly rejectSubmitButton: Locator;

    constructor(page: Page) {
        super(page);
        this.mentorNameHeading = this.page.getByRole('heading', { name: /^[A-Za-z\s]+$/ });
        this.applicantInformationSection = this.page.getByText('Applicant Information');
        this.applicationDecisionSection = this.page.getByText('Application Decision');
        this.educationSection = this.page.getByText('Education');
        this.workExperienceSection = this.page.getByText('Work Experience');
        this.certificationsSection = this.page.getByText('Certifications');
        this.motivationSection = this.page.getByText('Motivation');
        this.supportingDocumentsSection = this.page.getByText('Supporting Documents');
        this.closeButton = this.page.locator('div').filter({ hasText: /^Close$/ }).getByRole('button');

        // Decision making buttons
        this.approveButton = this.page.getByRole('button', { name: 'Approve' });
        this.rejectButton = this.page.getByRole('button', { name: 'Reject' });
        this.requestUpdateButton = this.page.getByRole('button', { name: 'Request Update' });

        // Request update form elements
        this.requestUpdateTextbox = this.page.getByRole('textbox', { name: 'What information do you need' });
        this.requestUpdateSubmitButton = this.page.getByRole('button', { name: 'Request Update' });

        // Reject form elements
        this.rejectionReasonTextbox = this.page.getByRole('textbox', { name: 'Rejection Reason (required)' });
        this.rejectSubmitButton = this.page.getByRole('button', { name: 'Reject' });
    }

    async verifyMentorNameHeading(expectedName: string): Promise<void> {
        const nameHeading = this.page.getByRole('heading', { name: expectedName });
        await expect(nameHeading).toBeVisible();
        await nameHeading.click();
    }

    async verifyApplicantInformationSection(): Promise<void> {
        await expect(this.applicantInformationSection).toBeVisible();
        await this.applicantInformationSection.click();
    }

    async verifyApplicationDecisionSection(): Promise<void> {
        await expect(this.applicationDecisionSection).toBeVisible();
        await this.applicationDecisionSection.click();
    }

    async verifyEducationSection(): Promise<void> {
        await expect(this.educationSection).toBeVisible();
        await this.educationSection.click();
    }

    async verifyWorkExperienceSection(): Promise<void> {
        await expect(this.workExperienceSection).toBeVisible();
        await this.workExperienceSection.click();
    }

    async verifyCertificationsSection(): Promise<void> {
        await expect(this.certificationsSection).toBeVisible();
        await this.certificationsSection.click();
    }

    async verifyMotivationSection(): Promise<void> {
        await expect(this.motivationSection).toBeVisible();
        await this.motivationSection.click();
    }

    async verifySupportingDocumentsSection(): Promise<void> {
        await expect(this.supportingDocumentsSection).toBeVisible();
        await this.supportingDocumentsSection.click();
    }

    async verifyAllApplicationSections(): Promise<void> {
        await this.verifyApplicantInformationSection();
        await this.verifyApplicationDecisionSection();
        await this.verifyEducationSection();
        await this.verifyWorkExperienceSection();
        await this.verifyCertificationsSection();
        await this.verifyMotivationSection();
        await this.verifySupportingDocumentsSection();
    }

    async verifyDetailedApplicationView(mentorName: string): Promise<void> {
        // First verify the mentor name heading
        await this.verifyMentorNameHeading(mentorName);

        // Then verify all required sections are present
        await this.verifyAllApplicationSections();
    }

    async clickCloseButton(): Promise<void> {
        await this.closeButton.click();
    }

    // Decision Making Functions
    async clickRequestUpdateButton(): Promise<void> {
        await this.requestUpdateButton.click();
    }

    async fillRequestUpdateReason(reason: string): Promise<void> {
        await this.requestUpdateTextbox.click();
        await this.requestUpdateTextbox.fill(reason);
    }

    async submitRequestUpdate(): Promise<void> {
        await this.requestUpdateSubmitButton.click();
    }

    async performRequestUpdate(reason: string): Promise<void> {
        await this.clickRequestUpdateButton();
        await this.fillRequestUpdateReason(reason);
        await this.requestUpdateTextbox.click(); // Additional click as per requirements
        await this.submitRequestUpdate();
    }

    async clickRejectButton(): Promise<void> {
        await this.rejectButton.click();
    }

    async fillRejectionReason(reason: string): Promise<void> {
        await this.rejectionReasonTextbox.click();
        await this.rejectionReasonTextbox.fill(reason);
    }

    async submitRejection(): Promise<void> {
        await this.rejectSubmitButton.click();
    }

    async performRejection(reason: string): Promise<void> {
        await this.clickRejectButton();
        await this.fillRejectionReason(reason);
        await this.submitRejection();
    }

    async clickApproveButton(): Promise<void> {
        await this.approveButton.click();
    }

    async performApproval(): Promise<void> {
        // Click approve button twice as per requirements
        await this.clickApproveButton();
        await this.clickApproveButton();
    }

    // Document Management Functions
    async previewSupportingDocuments(): Promise<void> {
        await this.verifySupportingDocumentsSection();
        // Additional document preview functionality can be added here
    }
}
